/**
 * Renderer process for the panel window
 * Handles UI interactions and communicates with main process
 */

import { WebAppModel, TabInfo } from '@shared/types/app.types';

class PanelRenderer {
  private apps: WebAppModel[] = [];
  private activeAppId: string | null = null;
  private isPinned = false;

  // Tab management
  private tabs: TabInfo[] = [];
  private activeTabId: string | null = null;

  // DOM elements
  private appListElement!: HTMLElement;
  private noAppsMessage!: HTMLElement;
  private webContentElement!: HTMLElement;
  private loadingIndicator!: HTMLElement;
  private pinButton!: HTMLButtonElement;
  private settingsButton!: HTMLButtonElement;
  private closeButton!: HTMLButtonElement;
  private addAppButton!: HTMLButtonElement;

  // Tab DOM elements
  private tabsContainer!: HTMLElement;
  private newTabButton!: HTMLButtonElement;

  // Modal state (used for preventing duplicate dialogs and cleanup)
  // @ts-ignore - Used for state tracking and cleanup
  private settingsDialog: HTMLElement | null = null;

  constructor() {
    console.log('PanelRenderer constructor called');
    this.initializeDOM();
    this.setupEventHandlers();
    this.setupThemeHandlers();

    // Delay initial data loading to ensure everything is ready
    setTimeout(() => {
      this.loadInitialData();
    }, 100);
  }

  private initializeDOM(): void {
    this.appListElement = document.getElementById('app-list')!;
    this.noAppsMessage = document.getElementById('no-apps-message')!;
    this.webContentElement = document.getElementById('web-content')!;
    this.loadingIndicator = document.getElementById('loading-indicator')!;
    this.pinButton = document.getElementById('pin-button') as HTMLButtonElement;
    this.settingsButton = document.getElementById('settings-button') as HTMLButtonElement;
    this.closeButton = document.getElementById('close-button') as HTMLButtonElement;
    this.addAppButton = document.getElementById('add-app-button') as HTMLButtonElement;

    // Tab elements
    this.tabsContainer = document.getElementById('tabs-container')!;
    this.newTabButton = document.getElementById('new-tab-button') as HTMLButtonElement;
  }

  private setupEventHandlers(): void {
    // Panel controls
    this.pinButton.addEventListener('click', () => this.togglePin());
    this.settingsButton.addEventListener('click', () => this.openSettings());
    this.closeButton.addEventListener('click', () => this.closePanel());
    this.addAppButton.addEventListener('click', () => this.showAddAppDialog());

    // Tab controls
    this.newTabButton.addEventListener('click', () => this.createNewTab());

    // Listen for events from main process
    window.sideView.on('app-created', (app: WebAppModel) => {
      this.apps.push(app);
      this.renderAppList();
    });

    window.sideView.on('app-updated', (app: WebAppModel) => {
      const index = this.apps.findIndex(a => a.id === app.id);
      if (index >= 0) {
        this.apps[index] = app;
        this.renderAppList();
      }
    });

    window.sideView.on('app-deleted', (appId: string) => {
      this.apps = this.apps.filter(a => a.id !== appId);
      if (this.activeAppId === appId) {
        this.activeAppId = null;
        this.clearWebContent();
      }
      this.renderAppList();
    });

    // Tab events
    window.sideView.on('tab-created', (tab: TabInfo) => {
      this.tabs.push(tab);
      this.renderTabs();
    });

    window.sideView.on('tab-closed', (tabId: string) => {
      this.tabs = this.tabs.filter(tab => tab.id !== tabId);
      this.renderTabs();
    });

    window.sideView.on('tab-activated', (tabId: string) => {
      this.setActiveTab(tabId);
    });

    window.sideView.on('app-activated', (appId: string) => {
      this.setActiveApp(appId);
    });
  }

  private setupThemeHandlers(): void {
    // Listen for theme changes from the main process
    window.addEventListener('theme-changed', (event: any) => {
      const { theme, isDark, source } = event.detail;
      console.log(`Theme changed: ${theme} (dark: ${isDark}, source: ${source})`);

      // Theme variables are already applied by the UIService
      // We can add any additional renderer-specific theme handling here
      this.updateThemeClasses(isDark);
    });

    // Load initial theme
    this.loadInitialTheme();
  }

  private async loadInitialTheme(): Promise<void> {
    try {
      const themeData = await window.sideView.theme?.get();
      if (themeData) {
        this.updateThemeClasses(themeData.isDark);
      }
    } catch (error) {
      console.warn('Failed to load initial theme:', error);
    }
  }

  private updateThemeClasses(isDark: boolean): void {
    // Update body classes for theme-specific styling
    document.body.classList.remove('theme-light', 'theme-dark');
    document.body.classList.add(isDark ? 'theme-dark' : 'theme-light');

    // Update any theme-dependent elements
    this.updateControlButtonIcons(isDark);
  }

  private updateControlButtonIcons(_isDark: boolean): void {
    // Update button icons based on theme if needed
    // For now, the emoji icons work well in both themes
  }

  private async loadInitialData(): Promise<void> {
    try {
      console.log('Loading initial data...');

      // Check if window.sideView is available
      if (!window.sideView) {
        console.error('window.sideView is not available! Preload script may not have loaded.');
        this.showError('Failed to initialize SideView API');
        return;
      }

      console.log('window.sideView is available:', Object.keys(window.sideView));

      // Load apps
      console.log('Requesting apps from main process...');
      try {
        this.apps = await window.sideView.apps.getAll();
        console.log('Received apps:', this.apps);
      } catch (appsError) {
        console.error('Failed to get apps:', appsError);
        this.showError('Failed to load applications');
        return;
      }

      // Load pin state
      try {
        this.isPinned = await window.sideView.panel.isPinned();
        this.updatePinButton();
      } catch (pinError) {
        console.error('Failed to get pin state:', pinError);
        // Continue anyway, this is not critical
      }

      // Load tabs
      try {
        this.tabs = await window.sideView.tabs.getAll();
        console.log('Loaded tabs:', this.tabs);
      } catch (tabsError) {
        console.error('Failed to load tabs:', tabsError);
        // Continue anyway, this is not critical
      }

      // Ensure tab bar is visible
      this.ensureTabBarVisibility();

      // Render UI
      this.renderTabs();
      this.renderAppList();

      // Activate first tab if available
      if (this.tabs.length > 0 && !this.activeTabId) {
        const activeTab = this.tabs.find(tab => tab.isActive) || this.tabs[0];
        if (activeTab) {
          console.log('Activating tab:', activeTab.title);
          await this.activateTab(activeTab.id);
        }
      } else if (this.apps.length > 0 && !this.activeAppId) {
        // Fallback to apps if no tabs
        const firstApp = this.apps[0];
        if (firstApp) {
          console.log('Activating first app:', firstApp.name);
          await this.activateApp(firstApp.id);
        }
      } else {
        console.log('No tabs or apps available to activate');
        this.showEmptyState();
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      this.showError('Failed to initialize panel');
    }
  }

  private ensureTabBarVisibility(): void {
    const tabBar = document.getElementById('tab-bar');
    if (tabBar) {
      // Always show tab bar - it's part of the core UI
      tabBar.style.display = 'flex';
      console.log('Tab bar visibility ensured - tab bar is now visible');

      // Also ensure the tabs container is visible
      const tabsContainer = document.getElementById('tabs-container');
      if (tabsContainer) {
        tabsContainer.style.display = 'flex';
        console.log('Tabs container visibility ensured');
      }

      // Ensure new tab button is visible
      const newTabButton = document.getElementById('new-tab-button');
      if (newTabButton) {
        newTabButton.style.display = 'flex';
        console.log('New tab button visibility ensured');
      }
    } else {
      console.error('Tab bar element not found in DOM!');
    }
  }

  private showEmptyState(): void {
    this.clearWebContent();
    // If no tabs exist, create a default tab
    if (this.tabs.length === 0) {
      console.log('No tabs found, creating default tab');
      this.createNewTab();
    }
  }

  private renderAppList(): void {
    if (this.apps.length === 0) {
      this.noAppsMessage.style.display = 'block';
      return;
    }

    this.noAppsMessage.style.display = 'none';

    const appItems = this.apps.map(app => this.createAppItem(app));
    this.appListElement.innerHTML = '';
    appItems.forEach(item => this.appListElement.appendChild(item));
  }

  private createAppItem(app: WebAppModel): HTMLElement {
    const item = document.createElement('div');
    item.className = `app-item ${app.id === this.activeAppId ? 'active' : ''}`;
    item.addEventListener('click', () => this.activateApp(app.id));

    const icon = document.createElement('div');
    icon.className = 'app-icon';
    if (app.iconPath) {
      icon.innerHTML = `<img src="${app.iconPath}" alt="${app.name}" style="width: 100%; height: 100%; border-radius: 4px;">`;
    } else {
      icon.textContent = app.name.charAt(0).toUpperCase();
    }

    const info = document.createElement('div');
    info.className = 'app-info';

    const name = document.createElement('div');
    name.className = 'app-name';
    name.textContent = app.name;

    const url = document.createElement('div');
    url.className = 'app-url';
    url.textContent = app.url;

    info.appendChild(name);
    info.appendChild(url);

    item.appendChild(icon);
    item.appendChild(info);

    // Add badge if app has notifications
    if (app.hasBadge && app.badgeCount && app.badgeCount > 0) {
      const badge = document.createElement('div');
      badge.className = 'app-badge';
      badge.textContent = app.badgeCount > 99 ? '99+' : app.badgeCount.toString();
      item.appendChild(badge);
    }

    return item;
  }

  private async activateApp(appId: string): Promise<void> {
    try {
      await window.sideView.apps.activate(appId);
      this.setActiveApp(appId);
    } catch (error) {
      console.error('Failed to activate app:', error);
    }
  }

  private setActiveApp(appId: string): void {
    this.activeAppId = appId;
    this.renderAppList(); // Re-render to update active state
    this.loadWebContent(appId);
  }

  private async loadWebContent(appId: string): Promise<void> {
    console.log('Loading web content for app:', appId);
    const app = this.apps.find(a => a.id === appId);
    if (!app) {
      console.error('App not found:', appId);
      return;
    }

    console.log('Loading app:', app.name, 'URL:', app.url);
    this.showLoading();

    try {
      // Activate the app in the main process - this will attach the BrowserView
      await window.sideView.apps.activate(appId);

      // Clear the web content area since BrowserView will overlay it
      this.webContentElement.innerHTML = '<div class="web-view-placeholder">Web content is loading...</div>';

      // Hide loading after a short delay to allow BrowserView to attach
      setTimeout(() => {
        this.hideLoading();
      }, 500);

      // Update last accessed time
      app.lastAccessed = new Date();

      console.log('Web app activated successfully:', app.name);

    } catch (error) {
      this.hideLoading();
      this.showError('Failed to load web application');
      console.error('Error loading web content:', error);
    }
  }

  private clearWebContent(): void {
    this.webContentElement.innerHTML = '<div class="loading-indicator">Select an app to view</div>';
  }

  private showLoading(): void {
    this.loadingIndicator.style.display = 'block';
  }

  private hideLoading(): void {
    this.loadingIndicator.style.display = 'none';
  }

  private showError(message: string): void {
    this.webContentElement.innerHTML = `<div class="loading-indicator" style="color: #ff4444;">${message}</div>`;
  }

  private async togglePin(): Promise<void> {
    this.isPinned = !this.isPinned;
    await window.sideView.panel.setPinned(this.isPinned);
    this.updatePinButton();
  }

  private updatePinButton(): void {
    this.pinButton.classList.toggle('pinned', this.isPinned);
    this.pinButton.title = this.isPinned ? 'Unpin Panel' : 'Pin Panel';
  }

  private openSettings(): void {
    this.showSettingsDialog();
  }



  private async showSettingsDialog(): Promise<void> {
    // Check if settings dialog is already open to prevent duplicates
    const existingOverlay = document.querySelector('.settings-overlay');
    if (existingOverlay) {
      return;
    }

    // Notify main process that modal is opening (with improved timing)
    try {
      await window.sideView.panel.setModalState(true);
    } catch (error) {
      console.error('Failed to set modal state:', error);
      // Continue anyway - modal can still work without BrowserView detachment
    }

    // Create settings dialog overlay
    const overlay = document.createElement('div');
    overlay.className = 'settings-overlay';
    this.settingsDialog = overlay;
    overlay.innerHTML = `
      <div class="settings-dialog">
        <div class="settings-header">
          <h2>Settings</h2>
          <button class="close-settings" title="Close">✕</button>
        </div>
        <div class="settings-content">
          <div class="settings-section">
            <h3>Panel</h3>
            <div class="setting-item">
              <label for="panel-width">Panel Width:</label>
              <input type="range" id="panel-width" min="250" max="800" step="10">
              <span class="setting-value" id="panel-width-value">400px</span>
            </div>
            <div class="setting-item">
              <label for="panel-height">Panel Height:</label>
              <input type="range" id="panel-height" min="400" max="1200" step="20">
              <span class="setting-value" id="panel-height-value">600px</span>
            </div>
            <div class="setting-item">
              <label for="panel-position">Position:</label>
              <select id="panel-position">
                <option value="left">Left</option>
                <option value="right">Right</option>
              </select>
            </div>
            <div class="setting-item">
              <label for="auto-hide">Auto Hide:</label>
              <input type="checkbox" id="auto-hide">
            </div>
          </div>

          <div class="settings-section">
            <h3>Edge Activation</h3>
            <div class="setting-item">
              <label for="edge-enabled">Enable Left Edge:</label>
              <input type="checkbox" id="edge-enabled">
            </div>
            <div class="setting-item">
              <label for="edge-position">Position:</label>
              <select id="edge-position">
                <option value="top">Top</option>
                <option value="middle">Middle</option>
                <option value="bottom">Bottom</option>
                <option value="full">Full Edge</option>
              </select>
            </div>
            <div class="setting-item">
              <label for="edge-size">Size (% of edge):</label>
              <input type="range" id="edge-size" min="10" max="100" step="5">
              <span class="setting-value" id="edge-size-value">50%</span>
            </div>
            <div class="setting-item">
              <label for="edge-width">Width (pixels):</label>
              <input type="range" id="edge-width" min="1" max="20" step="1">
              <span class="setting-value" id="edge-width-value">4px</span>
            </div>
          </div>

          <div class="settings-section">
            <h3>Theme</h3>
            <div class="setting-item">
              <label for="theme">Theme:</label>
              <select id="theme">
                <option value="system">System</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
              </select>
            </div>
          </div>

          <div class="settings-section">
            <h3>Hotkeys</h3>
            <div class="setting-item">
              <label for="hotkeys-enabled">Enable Hotkeys:</label>
              <input type="checkbox" id="hotkeys-enabled">
            </div>
            <div class="setting-item">
              <label for="toggle-hotkey">Toggle Panel:</label>
              <input type="text" id="toggle-hotkey" readonly placeholder="Click to set">
            </div>
          </div>
        </div>
        <div class="settings-footer">
          <button class="settings-btn cancel">Cancel</button>
          <button class="settings-btn save">Save</button>
        </div>
      </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .settings-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999; /* Very high z-index to stay above all other content including web content */
        backdrop-filter: blur(2px); /* Add blur effect for better visual separation */
      }

      .settings-dialog {
        background: var(--panel-background);
        border: 1px solid var(--panel-border);
        border-radius: 8px;
        width: 500px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        position: relative;
        z-index: 10000; /* Ensure dialog content stays above overlay */
      }

      .settings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid var(--panel-border);
      }

      .settings-header h2 {
        margin: 0;
        color: var(--text-primary);
        font-size: 18px;
      }

      .close-settings {
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
      }

      .close-settings:hover {
        background: var(--app-item-hover);
      }

      .settings-content {
        padding: 20px;
      }

      .settings-section {
        margin-bottom: 24px;
      }

      .settings-section h3 {
        margin: 0 0 12px 0;
        color: var(--text-primary);
        font-size: 14px;
        font-weight: 600;
      }

      .setting-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;
      }

      .setting-item label {
        color: var(--text-secondary);
        font-size: 13px;
        min-width: 120px;
      }

      .setting-item input, .setting-item select {
        background: var(--panel-background-secondary);
        border: 1px solid var(--panel-border);
        color: var(--text-primary);
        padding: 6px 8px;
        border-radius: 4px;
        font-size: 13px;
      }

      .setting-item input[type="range"] {
        flex: 1;
      }

      .setting-value {
        color: var(--text-secondary);
        font-size: 12px;
        min-width: 50px;
      }

      .settings-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        padding: 16px 20px;
        border-top: 1px solid var(--panel-border);
      }

      .settings-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        font-size: 13px;
        cursor: pointer;
      }

      .settings-btn.cancel {
        background: var(--panel-background-secondary);
        color: var(--text-secondary);
      }

      .settings-btn.save {
        background: var(--accent-color);
        color: var(--button-text);
      }

      .settings-btn:hover {
        opacity: 0.8;
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(overlay);

    // Load current settings and setup handlers
    this.setupSettingsDialog(overlay);
  }

  private async setupSettingsDialog(overlay: HTMLElement): Promise<void> {
    try {
      // Load current settings with retry logic and defensive programming
      let settings;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          settings = await window.sideView.config.get();

          // Validate settings structure
          if (settings && typeof settings === 'object') {
            // Merge settings with defaults to ensure all required properties exist
            const defaultSettings = this.getDefaultSettings();

            settings = {
              ui: {
                ...defaultSettings.ui,
                ...settings.ui,
                edgeActivation: {
                  ...defaultSettings.ui.edgeActivation,
                  ...settings.ui?.edgeActivation,
                  left: {
                    ...defaultSettings.ui.edgeActivation.left,
                    ...settings.ui?.edgeActivation?.left
                  },
                  right: {
                    ...defaultSettings.ui.edgeActivation.right,
                    ...settings.ui?.edgeActivation?.right
                  },
                  top: {
                    ...defaultSettings.ui.edgeActivation.top,
                    ...settings.ui?.edgeActivation?.top
                  },
                  bottom: {
                    ...defaultSettings.ui.edgeActivation.bottom,
                    ...settings.ui?.edgeActivation?.bottom
                  }
                }
              },
              webEngine: {
                ...defaultSettings.webEngine,
                ...settings.webEngine
              },
              notifications: {
                ...defaultSettings.notifications,
                ...settings.notifications
              },
              hotkeys: {
                ...defaultSettings.hotkeys,
                ...settings.hotkeys
              },
              updater: {
                ...defaultSettings.updater,
                ...settings.updater
              }
            };

            break;
          } else {
            throw new Error('Settings object is invalid or null');
          }
        } catch (error) {
          retryCount++;
          console.warn(`Settings load attempt ${retryCount} failed:`, error);
          if (retryCount >= maxRetries) {
            // Use default settings as fallback
            settings = this.getDefaultSettings();
            console.warn('Using default settings as fallback');
            break;
          }
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      if (!settings) {
        settings = this.getDefaultSettings();
        console.warn('Using default settings as final fallback');
      }

      // Validate settings structure and provide fallbacks
      const uiSettings = settings.ui || {};
      const edgeActivation = uiSettings.edgeActivation || {};
      const leftEdge = edgeActivation.left || {};
      const hotkeys = settings.hotkeys || {};

      // Populate form with proper null checks and fallbacks
      const panelWidth = overlay.querySelector('#panel-width') as HTMLInputElement;
      const panelWidthValue = overlay.querySelector('#panel-width-value') as HTMLElement;
      const panelHeight = overlay.querySelector('#panel-height') as HTMLInputElement;
      const panelHeightValue = overlay.querySelector('#panel-height-value') as HTMLElement;
      const panelPosition = overlay.querySelector('#panel-position') as HTMLSelectElement;
      const autoHide = overlay.querySelector('#auto-hide') as HTMLInputElement;
      const edgeEnabled = overlay.querySelector('#edge-enabled') as HTMLInputElement;
      const edgePosition = overlay.querySelector('#edge-position') as HTMLSelectElement;
      const edgeSize = overlay.querySelector('#edge-size') as HTMLInputElement;
      const edgeSizeValue = overlay.querySelector('#edge-size-value') as HTMLElement;
      const edgeWidth = overlay.querySelector('#edge-width') as HTMLInputElement;
      const edgeWidthValue = overlay.querySelector('#edge-width-value') as HTMLElement;
      const theme = overlay.querySelector('#theme') as HTMLSelectElement;
      const hotkeysEnabled = overlay.querySelector('#hotkeys-enabled') as HTMLInputElement;
      const toggleHotkey = overlay.querySelector('#toggle-hotkey') as HTMLInputElement;

      // Safe assignment with fallbacks
      const safeAssign = (element: HTMLInputElement | HTMLSelectElement | HTMLElement, getter: () => any, fallback: any, displaySuffix: string = '') => {
        try {
          const value = getter();
          if (value !== undefined && value !== null) {
            if (element instanceof HTMLInputElement) {
              element.value = typeof value === 'string' ? value : String(value);
            } else if (element instanceof HTMLSelectElement) {
              element.value = String(value);
            } else {
              element.textContent = `${value}${displaySuffix}`;
            }
          } else {
            // Use fallback
            if (element instanceof HTMLInputElement) {
              element.value = typeof fallback === 'string' ? fallback : String(fallback);
            } else if (element instanceof HTMLSelectElement) {
              element.value = String(fallback);
            } else {
              element.textContent = `${fallback}${displaySuffix}`;
            }
          }
        } catch (error) {
          console.warn('Error assigning value to element:', error);
          // Use fallback on any error
          if (element instanceof HTMLInputElement) {
            element.value = typeof fallback === 'string' ? fallback : String(fallback);
          } else if (element instanceof HTMLSelectElement) {
            element.value = String(fallback);
          } else {
            element.textContent = `${fallback}${displaySuffix}`;
          }
        }
      };

      // Apply values with fallbacks
      safeAssign(panelWidth, () => uiSettings.panelWidth, 280);
      safeAssign(panelWidthValue, () => uiSettings.panelWidth, 280, 'px');
      safeAssign(panelHeight, () => uiSettings.panelHeight, 600);
      safeAssign(panelHeightValue, () => uiSettings.panelHeight, 600, 'px');
      safeAssign(panelPosition, () => uiSettings.panelPosition, 'left');
      
      // Boolean assignments need special handling
      try {
        autoHide.checked = uiSettings.autoHide !== undefined ? uiSettings.autoHide : true;
      } catch (error) {
        console.warn('Error setting autoHide:', error);
        autoHide.checked = true;
      }
      
      try {
        edgeEnabled.checked = leftEdge.enabled !== undefined ? leftEdge.enabled : true;
      } catch (error) {
        console.warn('Error setting edgeEnabled:', error);
        edgeEnabled.checked = true;
      }
      
      safeAssign(edgePosition, () => leftEdge.position, 'middle');
      safeAssign(edgeSize, () => leftEdge.size, 50);
      safeAssign(edgeSizeValue, () => leftEdge.size, 50, '%');
      safeAssign(edgeWidth, () => leftEdge.width, 4);
      safeAssign(edgeWidthValue, () => leftEdge.width, 4, 'px');
      safeAssign(theme, () => uiSettings.theme, 'system');
      
      try {
        hotkeysEnabled.checked = hotkeys.enabled !== undefined ? hotkeys.enabled : true;
      } catch (error) {
        console.warn('Error setting hotkeysEnabled:', error);
        hotkeysEnabled.checked = true;
      }
      
      safeAssign(toggleHotkey, () => hotkeys.togglePanel, 'CommandOrControl+Alt+Right');

      // Setup event handlers
      panelWidth.addEventListener('input', () => {
        panelWidthValue.textContent = `${panelWidth.value}px`;
      });

      panelHeight.addEventListener('input', () => {
        panelHeightValue.textContent = `${panelHeight.value}px`;
      });

      edgeSize.addEventListener('input', () => {
        edgeSizeValue.textContent = `${edgeSize.value}%`;
      });

      edgeWidth.addEventListener('input', () => {
        edgeWidthValue.textContent = `${edgeWidth.value}px`;
      });

      // Close handlers
      const closeBtn = overlay.querySelector('.close-settings') as HTMLButtonElement;
      const cancelBtn = overlay.querySelector('.cancel') as HTMLButtonElement;

      const closeDialog = async () => {
        try {
          await window.sideView.panel.setModalState(false);
        } catch (error) {
          console.error('Failed to clear modal state:', error);
          // Continue with DOM cleanup anyway
        }

        // Clean up dialog elements
        try {
          if (overlay.parentNode) {
            document.body.removeChild(overlay);
          }

          // Clear the dialog reference
          this.settingsDialog = null;

        } catch (cleanupError) {
          console.error('Failed to clean up dialog elements:', cleanupError);
        }
      };

      closeBtn.addEventListener('click', closeDialog);
      cancelBtn.addEventListener('click', closeDialog);
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) closeDialog();
      });

      // Save handler
      const saveBtn = overlay.querySelector('.save') as HTMLButtonElement;
      saveBtn.addEventListener('click', async () => {
        try {
          // Validate inputs
          const widthValue = parseInt(panelWidth.value);
          const heightValue = parseInt(panelHeight.value);

          if (isNaN(widthValue) || widthValue < 250 || widthValue > 800) {
            this.showNotification('Panel width must be between 250 and 800 pixels', 'error');
            return;
          }

          if (isNaN(heightValue) || heightValue < 400 || heightValue > 1200) {
            this.showNotification('Panel height must be between 400 and 1200 pixels', 'error');
            return;
          }

          // Build updates object with safe property access
          const updates = {
            ui: {
              panelWidth: widthValue,
              panelHeight: heightValue,
              panelPosition: panelPosition.value as any,
              autoHide: autoHide.checked,
              theme: theme.value as any,
              animationDuration: uiSettings.animationDuration || 300,
              activationDelay: uiSettings.activationDelay || 500,
              edgeActivation: {
                ...edgeActivation,
                left: {
                  enabled: edgeEnabled.checked,
                  position: edgePosition.value as any,
                  size: parseInt(edgeSize.value) || 50,
                  width: parseInt(edgeWidth.value) || 4,
                  offset: leftEdge.offset || 0
                }
              }
            },
            hotkeys: {
              enabled: hotkeysEnabled.checked,
              togglePanel: toggleHotkey.value || 'CommandOrControl+Alt+Right',
              newTab: hotkeys.newTab || 'CommandOrControl+T',
              refresh: hotkeys.refresh || 'F5'
            }
          };

          await window.sideView.config.update(updates);
          closeDialog();

          // Show success message
          this.showNotification('Settings saved successfully', 'success');

          // Restart widget to ensure clean state after settings changes
          try {
            await window.sideView.panel.restartWidget();
            this.showNotification('Widget restarted for clean state', 'info');
          } catch (restartError) {
            console.warn('Failed to restart widget after settings save:', restartError);
            // Don't show error to user as settings were saved successfully
          }
        } catch (error) {
          console.error('Failed to save settings:', error);
          this.showNotification('Failed to save settings', 'error');
        }
      });

    } catch (error) {
      console.error('Failed to load settings:', error);
      this.showNotification('Failed to load settings - using defaults', 'error');

      // Close the dialog and show error state
      const closeDialog = async () => {
        try {
          await window.sideView.panel.setModalState(false);
        } catch (error) {
          console.error('Failed to clear modal state on error:', error);
        }

        if (overlay.parentNode) {
          document.body.removeChild(overlay);
        }
        this.settingsDialog = null;
      };

      // Add error message to dialog
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = `
        background: #ff4444;
        color: white;
        padding: 12px;
        margin: 16px;
        border-radius: 4px;
        font-size: 13px;
      `;
      errorDiv.textContent = `Settings loading failed: ${error instanceof Error ? error.message : String(error)}`;

      const dialogContent = overlay.querySelector('.settings-content');
      if (dialogContent) {
        dialogContent.insertBefore(errorDiv, dialogContent.firstChild);
      }

      // Auto-close after 5 seconds
      setTimeout(closeDialog, 5000);
    }
  }

  private getDefaultSettings(): any {
    return {
      ui: {
        panelWidth: 280,
        panelHeight: 600,
        panelPosition: 'right',
        theme: 'auto',
        animationDuration: 300,
        autoHide: true,
        activationDelay: 200,
        edgeActivation: {
          left: { enabled: false, position: 'middle', size: 50 },
          right: { enabled: true, position: 'middle', size: 50 },
          top: { enabled: false, position: 'middle', size: 50 },
          bottom: { enabled: false, position: 'middle', size: 50 }
        }
      },
      webEngine: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) SideView/1.0',
        enableDevTools: false,
        enableJavaScript: true,
        enableImages: true,
        enablePlugins: false,
        defaultZoomLevel: 1.0
      },
      notifications: {
        enabled: true,
        showBadges: true,
        soundEnabled: true,
        position: 'TopRight'
      },
      hotkeys: {
        togglePanel: 'CommandOrControl+Alt+Right',
        newTab: 'CommandOrControl+T',
        refresh: 'F5',
        enabled: true
      },
      updater: {
        enabled: true,
        checkInterval: 3600000,
        autoDownload: true,
        autoInstall: false,
        channel: 'Stable'
      }
    };
  }

  private showNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    const style = document.createElement('style');
    style.textContent = `
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        font-size: 13px;
        z-index: 2000;
        animation: slideIn 0.3s ease;
      }

      .notification-success { background: #4caf50; }
      .notification-error { background: #f44336; }
      .notification-info { background: #2196f3; }

      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  private async closePanel(): Promise<void> {
    await window.sideView.panel.hide();
  }

  private showAddAppDialog(): void {
    // Simple prompt for now - could be enhanced with a proper dialog
    const name = prompt('Enter app name:');
    if (!name) return;

    const url = prompt('Enter app URL:');
    if (!url) return;

    window.sideView.apps.create({
      name: name.trim(),
      url: url.trim(),
      sessionMode: 'isolated' as any
    }).catch(error => {
      console.error('Failed to create app:', error);
      alert('Failed to create app: ' + error.message);
    });
  }

  // Tab Management Methods
  private renderTabs(): void {
    this.tabsContainer.innerHTML = '';

    if (this.tabs.length === 0) {
      // Show a placeholder when no tabs exist
      const placeholder = document.createElement('div');
      placeholder.className = 'tabs-placeholder';
      placeholder.textContent = 'No tabs open - click + to create a new tab';
      placeholder.style.cssText = `
        color: var(--text-secondary);
        font-size: 12px;
        padding: 8px 12px;
        font-style: italic;
      `;
      this.tabsContainer.appendChild(placeholder);
    } else {
      this.tabs.forEach(tab => {
        const tabElement = this.createTabElement(tab);
        this.tabsContainer.appendChild(tabElement);
      });
    }
  }

  private createTabElement(tab: TabInfo): HTMLElement {
    const tabElement = document.createElement('div');
    tabElement.className = `tab ${tab.isActive ? 'active' : ''}`;
    tabElement.dataset['tabId'] = tab.id;

    // Favicon with better fallback and error handling
    const favicon = document.createElement('img');
    favicon.className = 'tab-favicon';

    // Create a better default favicon
    const defaultFavicon = 'data:image/svg+xml;base64,' + btoa(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM7 6a1 1 0 112 0v2a1 1 0 11-2 0V6z"/>
        <circle cx="8" cy="11" r="1"/>
      </svg>
    `);

    favicon.src = tab.favicon || defaultFavicon;
    favicon.alt = '';
    favicon.onerror = () => {
      favicon.src = defaultFavicon;
    };

    // Title with better truncation
    const title = document.createElement('span');
    title.className = 'tab-title';
    const displayTitle = tab.title || 'New Tab';
    title.textContent = displayTitle.length > 20 ? displayTitle.substring(0, 17) + '...' : displayTitle;
    title.title = displayTitle; // Full title on hover

    // Loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'tab-loading';
    loadingIndicator.innerHTML = '⟳';
    loadingIndicator.style.display = tab.isLoading ? 'flex' : 'none';

    // Close button with better styling
    const closeButton = document.createElement('button');
    closeButton.className = 'tab-close';
    closeButton.innerHTML = '×';
    closeButton.title = 'Close tab';
    closeButton.addEventListener('click', (e) => {
      e.stopPropagation();
      this.closeTab(tab.id);
    });

    // Tab click handler with visual feedback
    tabElement.addEventListener('click', () => {
      if (!tab.isActive) {
        this.activateTab(tab.id);
      }
    });

    // Add hover effect for better UX
    tabElement.addEventListener('mouseenter', () => {
      if (!tab.isActive) {
        tabElement.style.transform = 'translateY(-1px)';
      }
    });

    tabElement.addEventListener('mouseleave', () => {
      if (!tab.isActive) {
        tabElement.style.transform = '';
      }
    });

    tabElement.appendChild(favicon);
    tabElement.appendChild(title);
    if (tab.isLoading) {
      tabElement.appendChild(loadingIndicator);
    }
    tabElement.appendChild(closeButton);

    return tabElement;
  }

  private async createNewTab(): Promise<void> {
    try {
      // Show dialog for new tab creation
      const result = this.showNewTabDialog();
      if (!result) {
        return;
      }

      const { url, name } = result;

      // Enhanced validation
      if (!url || !url.trim()) {
        this.showNotification('URL is required for new tab', 'error');
        return;
      }

      if (!name || !name.trim()) {
        this.showNotification('Name is required for new tab', 'error');
        return;
      }

      // **ENHANCED FIX: Better URL validation with auto-correction**
      let validatedUrl = url.trim();
      try {
        new URL(validatedUrl);
      } catch (urlError) {
        // Try adding https:// if missing
        if (!validatedUrl.startsWith('http://') && !validatedUrl.startsWith('https://')) {
          validatedUrl = 'https://' + validatedUrl;
          try {
            new URL(validatedUrl);
          } catch (secondError) {
            this.showNotification('Invalid URL format. Please include http:// or https://', 'error');
            return;
          }
        } else {
          this.showNotification('Invalid URL format', 'error');
          return;
        }
      }

      // Show loading notification
      this.showNotification('Creating new tab...', 'info');
      console.log('[TAB-CREATE-UI] Validated URL:', validatedUrl);

      // Show loading notification
      this.showNotification('Creating new tab...', 'info');

      // Create tab with validated URL and better error handling
      const newTab = await this.createTabWithRetry({
        url: validatedUrl,
        name: name.trim(),
        sessionMode: 'isolated' as any,
        activate: true
      }, 3);

      // Ensure tab is in local state
      if (!this.tabs.find(tab => tab.id === newTab.id)) {
        this.tabs.push(newTab);
        this.renderTabs();
      }

      // Force immediate WebApp attachment with better error handling
      try {
        await this.forceWebAppAttachment(newTab.id);
      } catch (attachError) {
        // Don't fail the entire operation if attachment fails
      }

      this.showNotification('Tab created successfully', 'success');

    } catch (error) {

      // Better error messages based on error type
      let errorMessage = 'Failed to create new tab';
      if (error instanceof Error) {
        if (error.message.includes('Maximum number of tabs')) {
          errorMessage = 'Maximum number of tabs reached';
        } else if (error.message.includes('WebEngineService')) {
          errorMessage = 'Web engine not available. Please try again.';
        } else if (error.message.includes('AppManagerService')) {
          errorMessage = 'App manager not available. Please try again.';
        } else if (error.message.includes('not initialized')) {
          errorMessage = 'Services not ready. Please wait and try again.';
        }
      }

      this.showNotification(errorMessage, 'error');
    }
  }

  private async createTabWithRetry(request: any, maxRetries: number): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          await new Promise(resolve => setTimeout(resolve, 200 * attempt));
        }

        const newTab = await window.sideView.tabs.create(request);
        return newTab;

      } catch (error) {
        lastError = error as Error;

        if (attempt === maxRetries) {
          break;
        }
      }
    }

    throw new Error(`Tab creation failed after ${maxRetries} attempts. Last error: ${lastError?.message}`);
  }

  private showNewTabDialog(): { url: string; name: string } | null {
    // For now, use simple prompts - could be enhanced with a proper dialog
    let url = 'https://www.google.com';
    let name = 'New Tab';

    // Check if this is a user-initiated action (button click) vs automatic
    if (this.tabs.length > 0) {
      const userUrl = prompt('Enter URL for new tab:', 'https://');
      if (userUrl === null) return null; // User cancelled

      const userName = prompt('Enter name for new tab (optional):', '');
      if (userName === null) return null; // User cancelled

      url = userUrl.trim() || 'https://www.google.com';
      name = userName?.trim() || 'New Tab';

      // Validate URL
      try {
        new URL(url);
      } catch {
        // If URL is invalid, try adding https://
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          url = 'https://' + url;
        }
        try {
          new URL(url);
        } catch {
          this.showNotification('Invalid URL provided', 'error');
          return null;
        }
      }
    }

    return { url, name };
  }

  // **CRITICAL FIX: Enhanced Tab Activation with Service Status Tracking**
  private async activateTab(tabId: string): Promise<void> {
    console.log(`[TAB-ACTIVATION] Starting activation: ${tabId}`);

    // Check if tab exists in local state
    const tab = this.tabs.find(t => t.id === tabId);
    if (!tab) {
      console.error(`[TAB-ACTIVATION] ERROR: Tab ${tabId} not found in local state`);
      this.showNotification('Tab not found', 'error');
      return;
    }

    // Prevent multiple simultaneous activations of the same tab
    if (tab.isActive) {
      console.log(`[TAB-ACTIVATION] Tab ${tabId} already active, skipping`);
      return;
    }

    try {
      // Step 1: Update local UI state
      console.log(`[TAB-ACTIVATION] Step 1: Updating local UI state for ${tabId}`);
      this.setActiveTab(tabId);

      // Step 2: Activate in main process with immediate attachment
      console.log(`[TAB-ACTIVATION] Step 2: Calling main process activation for ${tabId}`);
      await this.activateTabWithRetry(tabId, 3);

      // Step 3: Force immediate WebApp attachment (this is the key fix)
      console.log(`[TAB-ACTIVATION] Step 3: Forcing immediate WebApp attachment for ${tabId}`);
      await this.forceWebAppAttachment(tabId);

      console.log(`[TAB-ACTIVATION] SUCCESS: Tab ${tabId} activated with immediate content`);
    } catch (error) {
      console.error(`[TAB-ACTIVATION] ERROR: Failed to activate tab ${tabId}:`, error);
      this.showNotification(`Failed to activate tab: ${(error as Error).message}`, 'error');

      // Revert local state if main process activation failed
      const previouslyActiveTab = this.tabs.find(tab => tab.isActive && tab.id !== tabId);
      if (previouslyActiveTab) {
        console.log(`[TAB-ACTIVATION] Reverting to previous tab: ${previouslyActiveTab.id}`);
        this.setActiveTab(previouslyActiveTab.id);
      } else {
        this.tabs.forEach(t => t.isActive = false);
        this.activeTabId = null;
        this.renderTabs();
      }
    }
  }

  private async activateTabWithRetry(tabId: string, maxRetries: number): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          await new Promise(resolve => setTimeout(resolve, 100 * attempt));
        }

        await window.sideView.tabs.activate(tabId);
        return; // Success!

      } catch (error) {
        lastError = error as Error;
        console.warn(`Attempt ${attempt} failed: ${(error as Error).message}`);

        if (attempt === maxRetries) {
          break;
        }
      }
    }

    throw new Error(`Tab activation failed after ${maxRetries} attempts. Last error: ${lastError?.message}`);
  }

  private async forceWebAppAttachment(tabId: string): Promise<void> {
    try {
      // Force the panel to show and attach the WebApp immediately
      // This is what normally happens only on hover - we need it to happen on tab activation
      await window.sideView.panel.attachActiveWebApp();

    } catch (error) {
      console.error(`Failed to attach WebApp ${tabId}:`, error);
      // Don't throw here - tab activation should still work even if attachment fails
    }
  }

  private setActiveTab(tabId: string): void {
    this.activeTabId = tabId;

    // Update tab states immediately for responsive UI
    this.tabs.forEach(tab => {
      tab.isActive = tab.id === tabId;
    });

    // Re-render tabs to show the change immediately
    this.renderTabs();
  }

  private async closeTab(tabId: string): Promise<void> {
    try {
      await window.sideView.tabs.close(tabId);

      // Remove from local tabs array
      this.tabs = this.tabs.filter(tab => tab.id !== tabId);

      // If this was the active tab, clear active state
      if (this.activeTabId === tabId) {
        this.activeTabId = null;
        this.clearWebContent();
      }

      this.renderTabs();
    } catch (error) {
      console.error('Failed to close tab:', error);
      this.showNotification('Failed to close tab', 'error');
    }
  }

  // **OVERLAY APPROACH: Modal state management methods no longer needed**
  // Removed clearModalStateWithRetry and performEmergencyWidgetRestart methods
  // since we no longer manipulate BrowserView state for modals
}

// Expose PanelRenderer globally for debugging
(window as any).PanelRenderer = PanelRenderer;

// Track renderer errors globally
(window as any).rendererErrors = [];

// Override console.error to track errors
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
  (window as any).rendererErrors.push(args.join(' '));
  originalConsoleError.apply(console, args);
};

// **CRITICAL FIX 1: Eliminate Dual Initialization (Dead Code)**
// The dual initialization was causing race conditions and state conflicts
// Only use DOMContentLoaded to ensure proper initialization order

let rendererInitialized = false;

function initializeRenderer() {
  if (rendererInitialized) {
    return;
  }

  try {
    const renderer = new PanelRenderer();
    (window as any).panelRendererInstance = renderer;
    rendererInitialized = true;
    console.log('Panel renderer initialized successfully');
  } catch (error) {
    console.error('Failed to initialize PanelRenderer:', error);
    (window as any).rendererErrors.push(`Init error: ${error}`);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeRenderer);
} else {
  initializeRenderer();
}
