/**
 * WebApp Host - Manages individual web application instances
 * Migrated from C# SideView.WebEngine.Services.WebAppHost
 */

import { EventEmitter } from 'events';
import { BrowserView, WebContents } from 'electron';
import { WebAppModel } from '@shared/types/app.types';
import { ConfigurationService } from '@modules/core/configuration-service';
import { NavigationEventArgs, TitleChangedEventArgs } from '@shared/types/events.types';

export class WebAppHost extends EventEmitter {
  public readonly app: WebAppModel;
  private readonly session: Electron.Session;
  private readonly configurationService: ConfigurationService;
  private readonly logger: Console;

  private browserView?: BrowserView;
  private webContents?: WebContents;
  private isInitialized = false;
  private isActive = false;
  private isDisposed = false;
  private currentUrl?: string;

  constructor(
    app: WebAppModel,
    session: Electron.Session,
    configurationService: ConfigurationService,
    logger: Console
  ) {
    super();
    this.app = app;
    this.session = session;
    this.configurationService = configurationService;
    this.logger = logger;
  }

  get App(): WebAppModel {
    return this.app;
  }

  get IsInitialized(): boolean {
    return this.isInitialized;
  }

  get IsActive(): boolean {
    return this.isActive;
  }

  get CurrentUrl(): string | undefined {
    return this.currentUrl;
  }

  get BrowserView(): BrowserView | undefined {
    return this.browserView;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized || this.isDisposed) {
      return;
    }

    try {
      this.logger.debug(`Initializing WebAppHost for app ${this.app.id}: ${this.app.name}`);

      // Create BrowserView with the app's session
      this.browserView = new BrowserView({
        webPreferences: {
          session: this.session,
          nodeIntegration: false,
          contextIsolation: true,
          webSecurity: true,
          allowRunningInsecureContent: false,
          experimentalFeatures: false,
          plugins: false,
          javascript: true,
          images: true,
          textAreasAreResizable: false,
          webgl: true,
          spellcheck: true
        }
      });

      this.webContents = this.browserView.webContents;
      this.setupWebContentsEvents();
      this.configureWebContents();

      this.isInitialized = true;
      this.logger.debug(`WebAppHost initialized successfully for app ${this.app.id}`);

    } catch (error) {
      this.logger.error(`Failed to initialize WebAppHost for app ${this.app.id}:`, error);

      // Cleanup on failure
      if (this.browserView) {
        try {
          this.browserView.webContents.close();
        } catch (cleanupError) {
          // Ignore cleanup errors
        }
        delete (this as any).browserView;
        delete (this as any).webContents;
      }

      throw error;
    }
  }

  async navigate(url: string): Promise<void> {
    if (!this.isInitialized || !this.webContents || this.isDisposed) {
      throw new Error('WebAppHost not initialized or disposed');
    }

    try {
      this.logger.debug(`Navigating app ${this.app.id} to: ${url}`);

      // Validate URL
      const validatedUrl = this.validateAndNormalizeUrl(url);
      
      // Load the URL
      await this.webContents.loadURL(validatedUrl);
      this.currentUrl = validatedUrl;

      // Update app model
      this.app.url = validatedUrl;
      this.app.lastAccessed = new Date();

    } catch (error) {
      this.logger.error(`Failed to navigate app ${this.app.id} to ${url}:`, error);
      throw error;
    }
  }

  async reload(): Promise<void> {
    if (!this.webContents || this.isDisposed) {
      throw new Error('WebAppHost not initialized or disposed');
    }

    try {
      this.logger.debug(`Reloading app ${this.app.id}`);
      this.webContents.reload();
    } catch (error) {
      this.logger.error(`Failed to reload app ${this.app.id}:`, error);
      throw error;
    }
  }

  async executeScript(script: string): Promise<any> {
    if (!this.webContents || this.isDisposed) {
      throw new Error('WebAppHost not initialized or disposed');
    }

    try {
      this.logger.debug(`Executing script in app ${this.app.id}`);
      return await this.webContents.executeJavaScript(script);
    } catch (error) {
      this.logger.error(`Failed to execute script in app ${this.app.id}:`, error);
      throw error;
    }
  }

  async setActive(active: boolean): Promise<void> {
    if (this.isActive === active) {
      return;
    }

    this.isActive = active;
    this.app.isActive = active;

    if (active) {
      this.app.lastAccessed = new Date();
    }

    this.logger.debug(`App ${this.app.id} active state changed to: ${active}`);
  }

  async dispose(): Promise<void> {
    if (this.isDisposed) {
      return;
    }

    this.logger.debug(`Disposing WebAppHost for app ${this.app.id}`);

    try {
      // Remove all event listeners
      this.removeAllListeners();

      // Destroy browser view
      if (this.browserView && !this.browserView.webContents.isDestroyed()) {
        this.browserView.webContents.close();
      }

      delete (this as any).browserView;
      delete (this as any).webContents;
      this.isDisposed = true;
      this.isInitialized = false;
      this.isActive = false;

      this.logger.debug(`WebAppHost disposed for app ${this.app.id}`);

    } catch (error) {
      this.logger.error(`Error disposing WebAppHost for app ${this.app.id}:`, error);
      throw error;
    }
  }

  private setupWebContentsEvents(): void {
    if (!this.webContents) {
      return;
    }

    // Navigation events
    this.webContents.on('did-finish-load', () => {
      this.onNavigationCompleted(true);
    });

    this.webContents.on('did-fail-load', (_event, _errorCode, errorDescription, _validatedURL) => {
      this.onNavigationCompleted(false, errorDescription);
    });

    // Title changes
    this.webContents.on('page-title-updated', (_event, title) => {
      this.onTitleChanged(title);
    });

    // Loading state
    this.webContents.on('did-start-loading', () => {
      this.emit('loading-state-changed', true);
    });

    this.webContents.on('did-stop-loading', () => {
      this.emit('loading-state-changed', false);
    });

    // Security - prevent popup windows
    this.webContents.setWindowOpenHandler(() => {
      return { action: 'deny' };
    });

    this.webContents.on('will-navigate', (event, navigationUrl) => {
      // Allow navigation within the same domain or to HTTPS URLs
      if (!this.isNavigationAllowed(navigationUrl)) {
        event.preventDefault();
        this.logger.warn(`Navigation blocked for app ${this.app.id}: ${navigationUrl}`);
      }
    });

    this.logger.debug(`WebContents events setup for app ${this.app.id}`);
  }

  private configureWebContents(): void {
    if (!this.webContents) {
      return;
    }

    const settings = this.configurationService.getSettings();

    // Set user agent
    this.webContents.setUserAgent(settings.webEngine.userAgent);

    // Set zoom level
    this.webContents.setZoomLevel(Math.log(settings.webEngine.defaultZoomLevel) / Math.log(1.2));

    // Configure dev tools
    if (!settings.webEngine.enableDevTools) {
      this.webContents.on('before-input-event', (event, input) => {
        if (input.key === 'F12' || (input.control && input.shift && input.key === 'I')) {
          event.preventDefault();
        }
      });
    }

    this.logger.debug(`WebContents configured for app ${this.app.id}`);
  }

  private validateAndNormalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      
      // Only allow HTTP and HTTPS
      if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
        throw new Error(`Unsupported protocol: ${urlObj.protocol}`);
      }

      return urlObj.toString();
    } catch (error) {
      throw new Error(`Invalid URL: ${url}`);
    }
  }

  private isNavigationAllowed(url: string): boolean {
    try {
      const urlObj = new URL(url);
      
      // Only allow HTTPS (and HTTP for localhost/development)
      if (urlObj.protocol === 'https:') {
        return true;
      }
      
      if (urlObj.protocol === 'http:' && 
          (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1')) {
        return true;
      }

      return false;
    } catch {
      return false;
    }
  }

  private onNavigationCompleted(_success: boolean, _error?: string): void {
    const args = new NavigationEventArgs(
      this.currentUrl || this.app.url,
      true // isMainFrame
    );

    this.emit('navigation-completed', args);
  }

  private onTitleChanged(title: string): void {
    const args = new TitleChangedEventArgs(
      title,
      this.app.id
    );

    this.emit('title-changed', args);
  }
}
